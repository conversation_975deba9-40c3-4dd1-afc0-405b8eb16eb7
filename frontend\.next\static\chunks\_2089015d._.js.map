{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/contract/frontend/src/components/SwapInterface.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ethers } from 'ethers';\nimport deployedAddresses from '../../lib/deployedAddresses.json';\n\n// Type for ethereum object\ninterface EthereumProvider {\n  request: (args: { method: string; params?: unknown[] }) => Promise<unknown>;\n  isMetaMask?: boolean;\n}\n\n// Import ABIs\nconst TokenA = {\n  abi: [\n    \"function balanceOf(address owner) view returns (uint256)\",\n    \"function approve(address spender, uint256 amount) returns (bool)\"\n  ]\n};\n\nconst TokenB = {\n  abi: [\n    \"function balanceOf(address owner) view returns (uint256)\"\n  ]\n};\n\nconst TokenSwap = {\n  abi: [\n    \"function swap(uint256 amount) returns (bool)\"\n  ]\n};\n\nexport default function SwapInterface() {\n  const [mounted, setMounted] = useState(false);\n  const [account, setAccount] = useState('');\n  const [tokenABalance, setTokenABalance] = useState('0');\n  const [tokenBBalance, setTokenBBalance] = useState('0');\n  const [swapAmount, setSwapAmount] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [needsTokens, setNeedsTokens] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n    // Delay wallet check to avoid hydration issues with extensions\n    const timer = setTimeout(() => {\n      checkIfWalletIsConnected();\n    }, 100);\n    return () => clearTimeout(timer);\n  }, []);\n\n  useEffect(() => {\n    if (account && mounted) {\n      updateBalances();\n    }\n  }, [account, mounted]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  async function checkIfWalletIsConnected() {\n    try {\n      if (typeof window === 'undefined') return;\n      const { ethereum } = window as { ethereum?: EthereumProvider };\n      if (!ethereum) return;\n\n      const accounts = await ethereum.request({ method: 'eth_accounts' }) as string[];\n      if (accounts.length > 0) {\n        setAccount(accounts[0]);\n      }\n    } catch (error) {\n      console.error(error);\n    }\n  }\n\n  async function connectWallet() {\n    try {\n      if (typeof window === 'undefined') return;\n      const { ethereum } = window as { ethereum?: EthereumProvider };\n      if (!ethereum) return;\n\n      const accounts = await ethereum.request({ method: 'eth_requestAccounts' }) as string[];\n      setAccount(accounts[0]);\n    } catch (error) {\n      console.error(error);\n    }\n  }\n\n  async function updateBalances() {\n    try {\n      if (typeof window === 'undefined') return;\n      const { ethereum } = window as { ethereum?: EthereumProvider };\n      if (!ethereum) return;\n      const provider = new ethers.BrowserProvider(ethereum);\n      const signer = await provider.getSigner();\n\n      // Check network\n      const network = await provider.getNetwork();\n      console.log('Current network:', network.chainId);\n      \n      if (network.chainId !== BigInt(3940)) {\n        console.error('Please switch to Nexus network (Chain ID: 3940)');\n        setTokenABalance('Switch to Nexus');\n        setTokenBBalance('Switch to Nexus');\n        return;\n      }\n\n      const tokenAContract = new ethers.Contract(deployedAddresses.TokenA, TokenA.abi, signer);\n      const tokenBContract = new ethers.Contract(deployedAddresses.TokenB, TokenB.abi, signer);\n\n      try {\n        const balanceA = await tokenAContract.balanceOf(account);\n        const formattedBalanceA = ethers.formatEther(balanceA);\n        setTokenABalance(formattedBalanceA);\n        \n        // Check if user needs tokens\n        if (parseFloat(formattedBalanceA) === 0) {\n          setNeedsTokens(true);\n        } else {\n          setNeedsTokens(false);\n        }\n      } catch (error) {\n        console.error('Error getting TokenA balance:', error);\n        setTokenABalance('0');\n        setNeedsTokens(true);\n      }\n\n      try {\n        const balanceB = await tokenBContract.balanceOf(account);\n        setTokenBBalance(ethers.formatEther(balanceB));\n      } catch (error) {\n        console.error('Error getting TokenB balance:', error);\n        setTokenBBalance('0');\n      }\n    } catch (error) {\n      console.error('Error in updateBalances:', error);\n      setTokenABalance('Error');\n      setTokenBBalance('Error');\n    }\n  }\n\n  async function handleSwap() {\n    if (!swapAmount) return;\n    setLoading(true);\n\n    try {\n      if (typeof window === 'undefined') return;\n      const { ethereum } = window as { ethereum?: EthereumProvider };\n      if (!ethereum) return;\n      const provider = new ethers.BrowserProvider(ethereum);\n      const signer = await provider.getSigner();\n\n      const tokenAContract = new ethers.Contract(deployedAddresses.TokenA, TokenA.abi, signer);\n      const swapContract = new ethers.Contract(deployedAddresses.TokenSwap, TokenSwap.abi, signer);\n\n      const amount = ethers.parseEther(swapAmount);\n\n      // First approve TokenSwap contract to spend TokenA\n      const approveTx = await tokenAContract.approve(deployedAddresses.TokenSwap, amount);\n      await approveTx.wait();\n\n      // Then perform the swap\n      const swapTx = await swapContract.swap(amount);\n      await swapTx.wait();\n\n      // Update balances after swap\n      await updateBalances();\n      setSwapAmount('');\n    } catch (error) {\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  }\n\n  // Prevent hydration errors by not rendering until mounted\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen bg-white relative\">\n        <div className=\"flex flex-col items-center justify-center min-h-screen\">\n          <h1 className=\"text-5xl font-normal mb-12 text-black\">Nexus Swap</h1>\n          <div className=\"text-gray-500\">Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-black relative overflow-hidden\" suppressHydrationWarning>\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-900/20 via-transparent to-cyan-900/20\"></div>\n        <div className=\"absolute top-20 left-20 w-32 h-32 bg-gradient-to-br from-cyan-400 to-blue-600 transform rotate-45 opacity-10 animate-pulse\"></div>\n        <div className=\"absolute bottom-20 right-20 w-40 h-40 bg-gradient-to-br from-pink-400 to-red-600 transform -rotate-12 opacity-10 animate-pulse delay-1000\"></div>\n        <div className=\"absolute top-1/2 left-1/4 w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-600 transform rotate-12 opacity-10 animate-pulse delay-500\"></div>\n      </div>\n\n      {/* Geometric shapes */}\n      <div className=\"absolute inset-0 pointer-events-none\">\n        <div className=\"absolute top-32 right-32 w-8 h-8 border-4 border-cyan-400 transform rotate-45 animate-bounce\"></div>\n        <div className=\"absolute bottom-40 left-40 w-6 h-6 bg-pink-500 transform rotate-12 animate-pulse\"></div>\n        <div className=\"absolute top-1/3 left-1/3 w-4 h-4 bg-yellow-400 transform -rotate-45\"></div>\n      </div>\n\n      {/* Connection status - floating brutal style */}\n      <div className=\"absolute top-8 right-8 z-50\">\n        <div className={`px-6 py-3 font-black text-sm uppercase tracking-wider transform rotate-3 border-4 shadow-lg transition-all duration-300 ${\n          account\n            ? 'bg-green-400 border-green-600 text-green-900 shadow-green-400/50 animate-pulse'\n            : 'bg-red-400 border-red-600 text-red-900 shadow-red-400/50'\n        }`}>\n          {account ? '● ONLINE' : '○ OFFLINE'}\n        </div>\n      </div>\n\n      {/* Main brutal container */}\n      <div className=\"min-h-screen flex items-center justify-center p-4 relative z-10\">\n        <div className=\"w-full max-w-6xl\">\n          {/* Title section with extreme brutalism */}\n          <div className=\"text-center mb-16 relative\">\n            <div className=\"relative inline-block\">\n              <h1 className=\"text-8xl md:text-9xl font-black text-white mb-4 transform -rotate-2 drop-shadow-2xl relative z-10\">\n                NEXUS\n              </h1>\n              <div className=\"absolute inset-0 text-8xl md:text-9xl font-black text-cyan-400 transform rotate-1 translate-x-2 translate-y-2 -z-10\">\n                NEXUS\n              </div>\n            </div>\n            <div className=\"relative inline-block\">\n              <h2 className=\"text-5xl md:text-6xl font-black text-cyan-400 transform rotate-1 drop-shadow-xl relative z-10\">\n                SWAP\n              </h2>\n              <div className=\"absolute inset-0 text-5xl md:text-6xl font-black text-pink-500 transform -rotate-1 translate-x-1 translate-y-1 -z-10\">\n                SWAP\n              </div>\n            </div>\n            <div className=\"flex justify-center mt-8 space-x-4\">\n              <div className=\"w-16 h-2 bg-gradient-to-r from-pink-500 to-cyan-500 transform rotate-1\"></div>\n              <div className=\"w-16 h-2 bg-gradient-to-r from-yellow-500 to-red-500 transform -rotate-1\"></div>\n              <div className=\"w-16 h-2 bg-gradient-to-r from-green-500 to-blue-500 transform rotate-2\"></div>\n            </div>\n          </div>\n\n          {/* Main content area */}\n          {!account ? (\n            <div className=\"text-center\">\n              <div className=\"relative inline-block group\">\n                <button\n                  type=\"button\"\n                  onClick={connectWallet}\n                  className=\"relative bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-16 py-8 text-2xl font-black uppercase tracking-wider transform -rotate-2 hover:rotate-0 transition-all duration-500 shadow-2xl hover:shadow-cyan-500/50 border-8 border-white z-10\"\n                >\n                  CONNECT WALLET\n                </button>\n                <div className=\"absolute inset-0 bg-gradient-to-r from-pink-500 to-red-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500\"></div>\n              </div>\n              <div className=\"mt-12 flex justify-center space-x-8\">\n                <div className=\"text-center\">\n                  <div className=\"w-4 h-4 bg-cyan-400 mx-auto mb-2 transform rotate-45\"></div>\n                  <p className=\"text-gray-400 font-mono text-sm\">SECURE</p>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"w-4 h-4 bg-pink-400 mx-auto mb-2 transform rotate-45\"></div>\n                  <p className=\"text-gray-400 font-mono text-sm\">FAST</p>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"w-4 h-4 bg-yellow-400 mx-auto mb-2 transform rotate-45\"></div>\n                  <p className=\"text-gray-400 font-mono text-sm\">BRUTAL</p>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 w-full max-w-7xl\">\n              {/* Balance Cards - Left Column */}\n              <div className=\"space-y-6\">\n                <div className=\"relative group\">\n                  <div className=\"bg-gradient-to-br from-purple-600 to-pink-600 p-8 transform -rotate-3 border-8 border-white shadow-2xl relative z-10 group-hover:rotate-0 transition-transform duration-500\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h3 className=\"text-white font-black text-lg uppercase tracking-wider\">TOKEN A</h3>\n                      <div className=\"w-6 h-6 bg-white transform rotate-45\"></div>\n                    </div>\n                    <p className=\"font-mono text-4xl font-black text-white mb-2\">{tokenABalance}</p>\n                    <div className=\"w-full h-2 bg-white\"></div>\n                  </div>\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-cyan-600 to-blue-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500\"></div>\n                </div>\n\n                <div className=\"relative group\">\n                  <div className=\"bg-gradient-to-br from-cyan-600 to-blue-600 p-8 transform rotate-2 border-8 border-white shadow-2xl relative z-10 group-hover:rotate-0 transition-transform duration-500\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h3 className=\"text-white font-black text-lg uppercase tracking-wider\">TOKEN B</h3>\n                      <div className=\"w-6 h-6 bg-white transform -rotate-45\"></div>\n                    </div>\n                    <p className=\"font-mono text-4xl font-black text-white mb-2\">{tokenBBalance}</p>\n                    <div className=\"w-full h-2 bg-white\"></div>\n                  </div>\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-600 to-orange-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500\"></div>\n                </div>\n              </div>\n\n              {/* Swap Form - Center Column */}\n              <div className=\"space-y-8\">\n                <div className=\"relative group\">\n                  <div className=\"bg-black p-8 transform rotate-1 border-8 border-white shadow-2xl relative z-10 group-hover:rotate-0 transition-transform duration-500\">\n                    <h3 className=\"text-white font-black text-xl uppercase tracking-wider mb-6 flex items-center\">\n                      <span className=\"mr-4\">SWAP AMOUNT</span>\n                      <div className=\"w-4 h-4 bg-cyan-400 transform rotate-45\"></div>\n                    </h3>\n                    <input\n                      type=\"number\"\n                      value={swapAmount}\n                      onChange={(e) => setSwapAmount(e.target.value)}\n                      placeholder=\"0.0\"\n                      className=\"w-full bg-white text-black px-6 py-6 text-3xl font-black font-mono border-8 border-gray-800 focus:outline-none focus:border-cyan-400 transform -rotate-1 focus:rotate-0 transition-transform duration-300\"\n                    />\n                    <div className=\"mt-6 flex justify-between items-center\">\n                      <span className=\"text-gray-400 font-mono text-sm\">TOKEN A → TOKEN B</span>\n                      <div className=\"flex space-x-2\">\n                        <div className=\"w-3 h-3 bg-cyan-400 transform rotate-45\"></div>\n                        <div className=\"w-3 h-3 bg-pink-400 transform rotate-45\"></div>\n                        <div className=\"w-3 h-3 bg-yellow-400 transform rotate-45\"></div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-purple-600 to-pink-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500\"></div>\n                </div>\n\n                {/* Swap Button */}\n                <div className=\"relative group\">\n                  <button\n                    type=\"button\"\n                    onClick={handleSwap}\n                    disabled={loading || !swapAmount}\n                    className=\"relative w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-8 text-3xl font-black uppercase tracking-wider transform -rotate-1 hover:rotate-0 transition-all duration-500 shadow-2xl hover:shadow-green-500/50 border-8 border-white disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed z-10\"\n                  >\n                    {loading ? '⚡ PROCESSING...' : '🚀 SWAP NOW'}\n                  </button>\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500\"></div>\n                </div>\n              </div>\n\n              {/* Info Panel - Right Column */}\n              <div className=\"space-y-6\">\n                {needsTokens && (\n                  <div className=\"relative group\">\n                    <div className=\"bg-gradient-to-r from-yellow-400 to-orange-500 p-8 transform rotate-2 border-8 border-white shadow-2xl relative z-10 group-hover:rotate-0 transition-transform duration-500\">\n                      <h3 className=\"text-black font-black text-xl uppercase tracking-wider mb-4 flex items-center\">\n                        <span className=\"mr-4\">⚠ NEED TOKENS?</span>\n                      </h3>\n                      <p className=\"text-black font-bold mb-6 text-lg\">\n                        You need TokenA to test the swap functionality. Contact the deployer to get test tokens.\n                      </p>\n                      <div className=\"bg-black p-4 transform -rotate-1\">\n                        <p className=\"text-green-400 font-mono text-sm break-all\">\n                          {account}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-red-500 to-pink-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500\"></div>\n                  </div>\n                )}\n\n                {/* Stats Panel */}\n                <div className=\"relative group\">\n                  <div className=\"bg-gradient-to-br from-gray-800 to-gray-900 p-8 transform -rotate-1 border-8 border-white shadow-2xl relative z-10 group-hover:rotate-0 transition-transform duration-500\">\n                    <h3 className=\"text-white font-black text-xl uppercase tracking-wider mb-6\">STATS</h3>\n                    <div className=\"space-y-4\">\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"text-gray-400 font-mono\">NETWORK</span>\n                        <span className=\"text-cyan-400 font-black\">NEXUS</span>\n                      </div>\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"text-gray-400 font-mono\">RATIO</span>\n                        <span className=\"text-pink-400 font-black\">1:1</span>\n                      </div>\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"text-gray-400 font-mono\">FEE</span>\n                        <span className=\"text-green-400 font-black\">0%</span>\n                      </div>\n                    </div>\n                    <div className=\"mt-6 w-full h-2 bg-gradient-to-r from-cyan-400 via-pink-400 to-yellow-400\"></div>\n                  </div>\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-cyan-600 to-blue-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500\"></div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Footer - Brutal style */}\n          <div className=\"mt-20 text-center\">\n            <div className=\"flex justify-center space-x-8 mb-8\">\n              <div className=\"w-8 h-8 bg-cyan-400 transform rotate-45 animate-pulse\"></div>\n              <div className=\"w-8 h-8 bg-pink-400 transform -rotate-45 animate-pulse delay-200\"></div>\n              <div className=\"w-8 h-8 bg-yellow-400 transform rotate-45 animate-pulse delay-400\"></div>\n            </div>\n            <p className=\"text-gray-500 font-mono text-sm uppercase tracking-wider\">\n              POWERED BY NEXUS BLOCKCHAIN\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAYA,cAAc;AACd,MAAM,SAAS;IACb,KAAK;QACH;QACA;KACD;AACH;AAEA,MAAM,SAAS;IACb,KAAK;QACH;KACD;AACH;AAEA,MAAM,YAAY;IAChB,KAAK;QACH;KACD;AACH;AAEe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,WAAW;YACX,+DAA+D;YAC/D,MAAM,QAAQ;iDAAW;oBACvB;gBACF;gDAAG;YACH;2CAAO,IAAM,aAAa;;QAC5B;kCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,WAAW,SAAS;gBACtB;YACF;QACF;kCAAG;QAAC;QAAS;KAAQ,GAAG,kDAAkD;IAE1E,eAAe;QACb,IAAI;YACF;;YACA,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,IAAI,CAAC,UAAU;YAEf,MAAM,WAAW,MAAM,SAAS,OAAO,CAAC;gBAAE,QAAQ;YAAe;YACjE,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,WAAW,QAAQ,CAAC,EAAE;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,eAAe;QACb,IAAI;YACF;;YACA,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,IAAI,CAAC,UAAU;YAEf,MAAM,WAAW,MAAM,SAAS,OAAO,CAAC;gBAAE,QAAQ;YAAsB;YACxE,WAAW,QAAQ,CAAC,EAAE;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,eAAe;QACb,IAAI;YACF;;YACA,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,IAAI,CAAC,UAAU;YACf,MAAM,WAAW,IAAI,mLAAA,CAAA,SAAM,CAAC,eAAe,CAAC;YAC5C,MAAM,SAAS,MAAM,SAAS,SAAS;YAEvC,gBAAgB;YAChB,MAAM,UAAU,MAAM,SAAS,UAAU;YACzC,QAAQ,GAAG,CAAC,oBAAoB,QAAQ,OAAO;YAE/C,IAAI,QAAQ,OAAO,KAAK,OAAO,OAAO;gBACpC,QAAQ,KAAK,CAAC;gBACd,iBAAiB;gBACjB,iBAAiB;gBACjB;YACF;YAEA,MAAM,iBAAiB,IAAI,mLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gGAAA,CAAA,UAAiB,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE;YACjF,MAAM,iBAAiB,IAAI,mLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gGAAA,CAAA,UAAiB,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE;YAEjF,IAAI;gBACF,MAAM,WAAW,MAAM,eAAe,SAAS,CAAC;gBAChD,MAAM,oBAAoB,mLAAA,CAAA,SAAM,CAAC,WAAW,CAAC;gBAC7C,iBAAiB;gBAEjB,6BAA6B;gBAC7B,IAAI,WAAW,uBAAuB,GAAG;oBACvC,eAAe;gBACjB,OAAO;oBACL,eAAe;gBACjB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,iBAAiB;gBACjB,eAAe;YACjB;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,eAAe,SAAS,CAAC;gBAChD,iBAAiB,mLAAA,CAAA,SAAM,CAAC,WAAW,CAAC;YACtC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,iBAAiB;YACjB,iBAAiB;QACnB;IACF;IAEA,eAAe;QACb,IAAI,CAAC,YAAY;QACjB,WAAW;QAEX,IAAI;YACF;;YACA,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,IAAI,CAAC,UAAU;YACf,MAAM,WAAW,IAAI,mLAAA,CAAA,SAAM,CAAC,eAAe,CAAC;YAC5C,MAAM,SAAS,MAAM,SAAS,SAAS;YAEvC,MAAM,iBAAiB,IAAI,mLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gGAAA,CAAA,UAAiB,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE;YACjF,MAAM,eAAe,IAAI,mLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gGAAA,CAAA,UAAiB,CAAC,SAAS,EAAE,UAAU,GAAG,EAAE;YAErF,MAAM,SAAS,mLAAA,CAAA,SAAM,CAAC,UAAU,CAAC;YAEjC,mDAAmD;YACnD,MAAM,YAAY,MAAM,eAAe,OAAO,CAAC,gGAAA,CAAA,UAAiB,CAAC,SAAS,EAAE;YAC5E,MAAM,UAAU,IAAI;YAEpB,wBAAwB;YACxB,MAAM,SAAS,MAAM,aAAa,IAAI,CAAC;YACvC,MAAM,OAAO,IAAI;YAEjB,6BAA6B;YAC7B,MAAM;YACN,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,0DAA0D;IAC1D,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIvC;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAiD,wBAAwB;;0BAEtF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAW,AAAC,2HAIhB,OAHC,UACI,mFACA;8BAEH,UAAU,aAAa;;;;;;;;;;;0BAK5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoG;;;;;;sDAGlH,6LAAC;4CAAI,WAAU;sDAAsH;;;;;;;;;;;;8CAIvI,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgG;;;;;;sDAG9G,6LAAC;4CAAI,WAAU;sDAAuH;;;;;;;;;;;;8CAIxI,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;wBAKlB,CAAC,wBACA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAE,WAAU;8DAAkC;;;;;;;;;;;;sDAEjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAE,WAAU;8DAAkC;;;;;;;;;;;;sDAEjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAE,WAAU;8DAAkC;;;;;;;;;;;;;;;;;;;;;;;iDAKrD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAyD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;;;;;;;;;;;;sEAEjB,6LAAC;4DAAE,WAAU;sEAAiD;;;;;;sEAC9D,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAGjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAyD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;;;;;;;;;;;;sEAEjB,6LAAC;4DAAE,WAAU;sEAAiD;;;;;;sEAC9D,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAKnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAK,WAAU;8EAAO;;;;;;8EACvB,6LAAC;oEAAI,WAAU;;;;;;;;;;;;sEAEjB,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAkC;;;;;;8EAClD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8DAIrB,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAIjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU,WAAW,CAAC;oDACtB,WAAU;8DAET,UAAU,oBAAoB;;;;;;8DAEjC,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAKnB,6LAAC;oCAAI,WAAU;;wCACZ,6BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAU;0EAAO;;;;;;;;;;;sEAEzB,6LAAC;4DAAE,WAAU;sEAAoC;;;;;;sEAGjD,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;0EACV;;;;;;;;;;;;;;;;;8DAIP,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAKnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA8D;;;;;;sEAC5E,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAA0B;;;;;;sFAC1C,6LAAC;4EAAK,WAAU;sFAA2B;;;;;;;;;;;;8EAE7C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAA0B;;;;;;sFAC1C,6LAAC;4EAAK,WAAU;sFAA2B;;;;;;;;;;;;8EAE7C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAA0B;;;;;;sFAC1C,6LAAC;4EAAK,WAAU;sFAA4B;;;;;;;;;;;;;;;;;;sEAGhD,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAE,WAAU;8CAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpF;GA/WwB;KAAA", "debugId": null}}]}