const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("Deploying contracts...");

  // Deploy TokenA dengan initial supply 1,000,000 tokens (18 decimals)
  const TokenA = await hre.ethers.getContractFactory("TokenA");
  const tokenA = await TokenA.deploy(hre.ethers.parseEther("1000000"));
  await tokenA.waitForDeployment();
  const tokenAAddress = await tokenA.getAddress();
  console.log(`TokenA deployed to: ${tokenAAddress}`);

  // Deploy TokenB dengan initial supply 1,000,000 tokens (18 decimals)
  const TokenB = await hre.ethers.getContractFactory("TokenB");
  const tokenB = await TokenB.deploy(hre.ethers.parseEther("1000000"));
  await tokenB.waitForDeployment();
  const tokenBAddress = await tokenB.getAddress();
  console.log(`TokenB deployed to: ${tokenBAddress}`);

  // Deploy TokenSwap
  const TokenSwap = await hre.ethers.getContractFactory("TokenSwap");
  const tokenSwap = await TokenSwap.deploy(tokenAAddress, tokenBAddress);
  await tokenSwap.waitForDeployment();
  const tokenSwapAddress = await tokenSwap.getAddress();
  console.log(`TokenSwap deployed to: ${tokenSwapAddress}`);

  // Transfer some TokenB to TokenSwap contract untuk liquidity
  console.log("Transferring TokenB to TokenSwap contract for liquidity...");
  const transferAmount = hre.ethers.parseEther("500000"); // 500,000 tokens
  const transferTx = await tokenB.transfer(tokenSwapAddress, transferAmount);
  await transferTx.wait();
  console.log(`Transferred ${hre.ethers.formatEther(transferAmount)} TokenB to TokenSwap contract`);

  // Save addresses to file
  const addresses = {
    TokenA: tokenAAddress,
    TokenB: tokenBAddress,
    TokenSwap: tokenSwapAddress,
    network: "nexus",
    deployedAt: new Date().toISOString()
  };

  const filePath = path.join(__dirname, "..", "deployedAddresses.json");
  fs.writeFileSync(filePath, JSON.stringify(addresses, null, 2));
  console.log(`Addresses saved to deployedAddresses.json`);

  // Display summary
  console.log("\n=== Deployment Summary ===");
  console.log(`TokenA: ${tokenAAddress}`);
  console.log(`TokenB: ${tokenBAddress}`);
  console.log(`TokenSwap: ${tokenSwapAddress}`);
  console.log(`Network: Nexus Testnet`);
  console.log(`TokenSwap Liquidity: ${hre.ethers.formatEther(transferAmount)} TokenB`);
}

main().catch((error) => {
  console.error("Deployment failed:", error);
  process.exitCode = 1;
});
