module.exports = {

"[project]/src/app/favicon.ico (static in ecmascript)": (() => {{

throw new Error("An error occurred while generating the chunk item [project]/src/app/favicon.ico (static in ecmascript)\n\nCaused by:\n- StaticAsset::path: not found\n\nDebug info:\n- An error occurred while generating the chunk item [project]/src/app/favicon.ico (static in ecmascript)\n- Execution of *EcmascriptChunkItemContent::module_factory failed\n- Execution of <StaticUrlJsChunkItem as EcmascriptChunkItem>::content failed\n- Execution of <StaticOutputAsset as OutputAsset>::path failed\n- StaticAsset::path: not found");

}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const e = new Error("Could not parse module '[project]/src/app/favicon.ico.mjs'\n\nInput image not found\n\nDebug info:\n- Execution of <StructuredImageFileSource as Asset>::content failed\n- Execution of get_meta_data failed\n- Input image not found");
e.code = 'MODULE_UNPARSABLE';
throw e;
}}),

};