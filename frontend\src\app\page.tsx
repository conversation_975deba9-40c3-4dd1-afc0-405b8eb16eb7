import dynamic from 'next/dynamic';

// Import SwapInterface without SSR to prevent hydration errors
const SwapInterface = dynamic(() => import('../components/SwapInterface'), {
  ssr: false,
  loading: () => (
    <div className="min-h-screen bg-white relative">
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-5xl font-normal mb-12 text-black">Nexus Swap</h1>
        <div className="text-gray-500">Loading...</div>
      </div>
    </div>
  )
});

export default function Home() {
  return <SwapInterface />;
}
