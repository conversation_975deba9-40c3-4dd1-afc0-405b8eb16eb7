{"[project]/src/components/SwapInterface.tsx [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/src/components/SwapInterface.tsx [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/node_modules_ethers_lib_esm_utils_072fb278._.js", "static/chunks/node_modules_ethers_lib_esm_abi_b5bc55b4._.js", "static/chunks/node_modules_ethers_lib_esm_providers_c516f8ef._.js", "static/chunks/node_modules_ethers_lib_esm_96a693d7._.js", "static/chunks/node_modules_@noble_curves_esm_cf8dbd06._.js", "static/chunks/node_modules_fdc9948a._.js", "static/chunks/_2089015d._.js", "static/chunks/src_components_SwapInterface_tsx_036824ab._.js"]}}