{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/contract/frontend/src/app/page.tsx"], "sourcesContent": ["import dynamic from 'next/dynamic';\n\n// Import SwapInterface without SSR to prevent hydration errors\nconst SwapInterface = dynamic(() => import('../components/SwapInterface'), {\n  ssr: false,\n  loading: () => (\n    <div className=\"min-h-screen bg-white relative\">\n      <div className=\"flex flex-col items-center justify-center min-h-screen\">\n        <h1 className=\"text-5xl font-normal mb-12 text-black\">Nexus Swap</h1>\n        <div className=\"text-gray-500\">Loading...</div>\n      </div>\n    </div>\n  )\n});\n\nexport default function Home() {\n  return <SwapInterface />;\n}\n"], "names": [], "mappings": ";;;;AAAA;;;;AAEA,+DAA+D;AAC/D,MAAM,gBAAgB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;;;;IAC5B,KAAK;IACL,SAAS,kBACP,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAMxB,SAAS;IACtB,qBAAO,8OAAC;;;;;AACV", "debugId": null}}]}