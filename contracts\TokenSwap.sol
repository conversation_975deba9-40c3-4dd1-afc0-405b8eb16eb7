// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract TokenSwap {
    IERC20 public tokenA;
    IERC20 public tokenB;

    constructor(address _tokenA, address _tokenB) {
        tokenA = IERC20(_tokenA);
        tokenB = IERC20(_tokenB);
    }

    function swap(uint256 amount) external returns (bool) {
        require(tokenA.transferFrom(msg.sender, address(this), amount), "Transfer Token A failed");
        require(tokenB.transfer(msg.sender, amount), "Transfer Token B failed");
        return true;
    }
}
