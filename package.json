{"name": "contract", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "start": "cd frontend && npm start", "deploy": "npx hardhat run scripts/deploy.js --network nexus", "give-tokens": "npx hardhat run scripts/giveTokens.js --network nexus"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^6.1.0", "hardhat": "^2.26.1"}, "dependencies": {"@openzeppelin/contracts": "^5.4.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "dotenv": "^17.2.0", "ethers": "^6.15.0", "react": "^19.1.0", "react-dom": "^19.1.0"}}