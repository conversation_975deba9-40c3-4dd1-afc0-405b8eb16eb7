{"version": 3, "sources": [], "sections": [{"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/contract/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ethers } from 'ethers';\nimport deployedAddresses from '../../lib/deployedAddresses.json';\n\n// Import ABIs\nconst TokenA = {\n  abi: [\n    \"function balanceOf(address owner) view returns (uint256)\",\n    \"function approve(address spender, uint256 amount) returns (bool)\"\n  ]\n};\n\nconst TokenB = {\n  abi: [\n    \"function balanceOf(address owner) view returns (uint256)\"\n  ]\n};\n\nconst TokenSwap = {\n  abi: [\n    \"function swap(uint256 amount) returns (bool)\"\n  ]\n};\n\nexport default function Home() {\n  const [mounted, setMounted] = useState(false);\n  const [account, setAccount] = useState('');\n  const [tokenABalance, setTokenABalance] = useState('0');\n  const [tokenBBalance, setTokenBBalance] = useState('0');\n  const [swapAmount, setSwapAmount] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n    checkIfWalletIsConnected();\n  }, []);\n\n  useEffect(() => {\n    if (account && mounted) {\n      updateBalances();\n    }\n  }, [account, mounted]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  async function checkIfWalletIsConnected() {\n    try {\n      if (typeof window === 'undefined') return;\n      const { ethereum } = window as any;\n      if (!ethereum) return;\n\n      const accounts = await ethereum.request({ method: 'eth_accounts' });\n      if (accounts.length > 0) {\n        setAccount(accounts[0]);\n      }\n    } catch (error) {\n      console.error(error);\n    }\n  }\n\n  async function connectWallet() {\n    try {\n      if (typeof window === 'undefined') return;\n      const { ethereum } = window as any;\n      if (!ethereum) return;\n\n      const accounts = await ethereum.request({ method: 'eth_requestAccounts' });\n      setAccount(accounts[0]);\n    } catch (error) {\n      console.error(error);\n    }\n  }\n\n  async function updateBalances() {\n    try {\n      const { ethereum } = window as any;\n      const provider = new ethers.BrowserProvider(ethereum);\n      const signer = await provider.getSigner();\n\n      const tokenAContract = new ethers.Contract(deployedAddresses.TokenA, TokenA.abi, signer);\n      const tokenBContract = new ethers.Contract(deployedAddresses.TokenB, TokenB.abi, signer);\n\n      const balanceA = await tokenAContract.balanceOf(account);\n      const balanceB = await tokenBContract.balanceOf(account);\n\n      setTokenABalance(ethers.formatEther(balanceA));\n      setTokenBBalance(ethers.formatEther(balanceB));\n    } catch (error) {\n      console.error(error);\n    }\n  }\n\n  async function handleSwap() {\n    if (!swapAmount) return;\n    setLoading(true);\n\n    try {\n      const { ethereum } = window as any;\n      const provider = new ethers.BrowserProvider(ethereum);\n      const signer = await provider.getSigner();\n\n      const tokenAContract = new ethers.Contract(deployedAddresses.TokenA, TokenA.abi, signer);\n      const swapContract = new ethers.Contract(deployedAddresses.TokenSwap, TokenSwap.abi, signer);\n\n      const amount = ethers.parseEther(swapAmount);\n\n      // First approve TokenSwap contract to spend TokenA\n      const approveTx = await tokenAContract.approve(deployedAddresses.TokenSwap, amount);\n      await approveTx.wait();\n\n      // Then perform the swap\n      const swapTx = await swapContract.swap(amount);\n      await swapTx.wait();\n\n      // Update balances after swap\n      await updateBalances();\n      setSwapAmount('');\n    } catch (error) {\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  }\n\n  // Prevent hydration errors by not rendering until mounted\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen bg-white relative\">\n        <div className=\"flex flex-col items-center justify-center min-h-screen\">\n          <h1 className=\"text-5xl font-normal mb-12 text-black\">Nexus Swap</h1>\n          <div className=\"text-gray-500\">Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white relative\">\n      {/* Connection status */}\n      <div className=\"absolute top-6 right-6\">\n        <p className=\"text-sm text-gray-600\">\n          {account ? 'Connected' : 'Not Connected'}\n        </p>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col items-center justify-center min-h-screen\">\n        <h1 className=\"text-5xl font-normal mb-12 text-black\">Nexus Swap</h1>\n\n        {!account ? (\n          <button\n            type=\"button\"\n            onClick={connectWallet}\n            className=\"bg-black text-white px-6 py-3 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors\"\n          >\n            Connect Wallet\n          </button>\n        ) : (\n          <div className=\"space-y-8 w-full max-w-sm\">\n            <div className=\"grid grid-cols-2 gap-6\">\n              <div>\n                <p className=\"text-sm text-gray-600 mb-1\">Token A Balance</p>\n                <p className=\"font-mono text-xl\">{tokenABalance}</p>\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600 mb-1\">Token B Balance</p>\n                <p className=\"font-mono text-xl\">{tokenBBalance}</p>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm text-gray-600 mb-2\">\n                Swap Amount (Token A)\n              </label>\n              <input\n                type=\"number\"\n                value={swapAmount}\n                onChange={(e) => setSwapAmount(e.target.value)}\n                placeholder=\"0.0\"\n                className=\"w-full bg-gray-50 border border-gray-200 px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-black font-mono\"\n              />\n            </div>\n\n            <button\n              type=\"button\"\n              onClick={handleSwap}\n              disabled={loading || !swapAmount}\n              className=\"w-full bg-black text-white py-3 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors disabled:bg-gray-300\"\n            >\n              {loading ? 'Processing...' : 'Swap'}\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,cAAc;AACd,MAAM,SAAS;IACb,KAAK;QACH;QACA;KACD;AACH;AAEA,MAAM,SAAS;IACb,KAAK;QACH;KACD;AACH;AAEA,MAAM,YAAY;IAChB,KAAK;QACH;KACD;AACH;AAEe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,SAAS;YACtB;QACF;IACF,GAAG;QAAC;QAAS;KAAQ,GAAG,kDAAkD;IAE1E,eAAe;QACb,IAAI;YACF,wCAAmC;;;YACnC,MAAQ;YAGR,MAAM;QAIR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,eAAe;QACb,IAAI;YACF,wCAAmC;;;YACnC,MAAQ;YAGR,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,eAAe;QACb,IAAI;YACF,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,MAAM,WAAW,IAAI,gLAAA,CAAA,SAAM,CAAC,eAAe,CAAC;YAC5C,MAAM,SAAS,MAAM,SAAS,SAAS;YAEvC,MAAM,iBAAiB,IAAI,gLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gGAAA,CAAA,UAAiB,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE;YACjF,MAAM,iBAAiB,IAAI,gLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gGAAA,CAAA,UAAiB,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE;YAEjF,MAAM,WAAW,MAAM,eAAe,SAAS,CAAC;YAChD,MAAM,WAAW,MAAM,eAAe,SAAS,CAAC;YAEhD,iBAAiB,gLAAA,CAAA,SAAM,CAAC,WAAW,CAAC;YACpC,iBAAiB,gLAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,eAAe;QACb,IAAI,CAAC,YAAY;QACjB,WAAW;QAEX,IAAI;YACF,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,MAAM,WAAW,IAAI,gLAAA,CAAA,SAAM,CAAC,eAAe,CAAC;YAC5C,MAAM,SAAS,MAAM,SAAS,SAAS;YAEvC,MAAM,iBAAiB,IAAI,gLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gGAAA,CAAA,UAAiB,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE;YACjF,MAAM,eAAe,IAAI,gLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gGAAA,CAAA,UAAiB,CAAC,SAAS,EAAE,UAAU,GAAG,EAAE;YAErF,MAAM,SAAS,gLAAA,CAAA,SAAM,CAAC,UAAU,CAAC;YAEjC,mDAAmD;YACnD,MAAM,YAAY,MAAM,eAAe,OAAO,CAAC,gGAAA,CAAA,UAAiB,CAAC,SAAS,EAAE;YAC5E,MAAM,UAAU,IAAI;YAEpB,wBAAwB;YACxB,MAAM,SAAS,MAAM,aAAa,IAAI,CAAC;YACvC,MAAM,OAAO,IAAI;YAEjB,6BAA6B;YAC7B,MAAM;YACN,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,0DAA0D;IAC1D,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIvC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BACV,UAAU,cAAc;;;;;;;;;;;0BAK7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;oBAErD,CAAC,wBACA,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;6CAID,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAEpC,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;0CAItC,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAmC;;;;;;kDAGpD,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAId,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU,WAAW,CAAC;gCACtB,WAAU;0CAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}]}