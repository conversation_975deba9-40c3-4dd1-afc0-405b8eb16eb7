const hre = require("hardhat");
const deployedAddresses = require("../deployedAddresses.json");

async function main() {
  // Get the user address from command line argument
  const userAddress = process.argv[2];
  
  if (!userAddress) {
    console.log("Usage: npx hardhat run scripts/giveTokens.js --network nexus <user_address>");
    console.log("Example: npx hardhat run scripts/giveTokens.js --network nexus ******************************************");
    process.exit(1);
  }

  console.log(`Giving tokens to user: ${userAddress}`);

  // Get the deployer account (should have all the tokens)
  const [deployer] = await hre.ethers.getSigners();
  console.log(`Deployer address: ${deployer.address}`);

  // Connect to TokenA contract
  const TokenA = await hre.ethers.getContractFactory("TokenA");
  const tokenA = TokenA.attach(deployedAddresses.TokenA);

  // Connect to TokenB contract  
  const TokenB = await hre.ethers.getContractFactory("TokenB");
  const tokenB = TokenB.attach(deployedAddresses.TokenB);

  // Amount to give (1000 tokens)
  const amount = hre.ethers.parseEther("1000");

  try {
    // Transfer TokenA to user
    console.log("Transferring 1000 TokenA to user...");
    const txA = await tokenA.transfer(userAddress, amount);
    await txA.wait();
    console.log(`✅ TokenA transfer completed: ${txA.hash}`);

    // Transfer TokenB to user (for testing purposes)
    console.log("Transferring 1000 TokenB to user...");
    const txB = await tokenB.transfer(userAddress, amount);
    await txB.wait();
    console.log(`✅ TokenB transfer completed: ${txB.hash}`);

    // Check balances
    const balanceA = await tokenA.balanceOf(userAddress);
    const balanceB = await tokenB.balanceOf(userAddress);

    console.log("\n=== User Balances ===");
    console.log(`TokenA: ${hre.ethers.formatEther(balanceA)}`);
    console.log(`TokenB: ${hre.ethers.formatEther(balanceB)}`);
    console.log("\n✅ Tokens successfully transferred!");
    console.log("You can now test the swap functionality in the frontend.");

  } catch (error) {
    console.error("❌ Error transferring tokens:", error);
  }
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
