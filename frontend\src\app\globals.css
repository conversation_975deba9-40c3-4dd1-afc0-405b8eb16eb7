@import "tailwindcss/preflight";
@import "tailwindcss/utilities";

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 0, 0, 0;
  --background-end-rgb: 0, 0, 0;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-start-rgb));
  font-family: system-ui, -apple-system, sans-serif;
  overflow-x: hidden;
}

/* Custom brutal animations */
@keyframes brutal-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px) rotate(-1deg); }
  75% { transform: translateX(2px) rotate(1deg); }
}

@keyframes brutal-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(34, 211, 238, 0.3); }
  50% { box-shadow: 0 0 40px rgba(34, 211, 238, 0.6), 0 0 60px rgba(236, 72, 153, 0.3); }
}

@keyframes brutal-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(2deg); }
}

.brutal-shake:hover {
  animation: brutal-shake 0.5s ease-in-out;
}

.brutal-glow {
  animation: brutal-glow 2s ease-in-out infinite;
}

.brutal-float {
  animation: brutal-float 3s ease-in-out infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #000;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #06b6d4, #ec4899);
  border-radius: 0;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #ec4899, #06b6d4);
}
