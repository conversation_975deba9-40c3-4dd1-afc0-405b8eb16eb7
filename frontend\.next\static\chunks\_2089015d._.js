(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/lib/deployedAddresses.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"TokenA\":\"0xFcb75B75A70d4db7a0e74780f4E170C367059364\",\"TokenB\":\"0x2C2a163fEbf50d3c21269E3E2ecacFC973D41ab4\",\"TokenSwap\":\"0x0EfA7F18146F7436db446b4548456c959338b1E2\",\"network\":\"nexus\",\"deployedAt\":\"2025-07-24T06:43:47.473Z\"}"));}),
"[project]/src/components/SwapInterface.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {

var { k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
const e = new Error("Could not parse module '[project]/src/components/SwapInterface.tsx'");
e.code = 'MODULE_UNPARSABLE';
throw e;
}}),
"[project]/src/components/SwapInterface.tsx [app-client] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/components/SwapInterface.tsx [app-client] (ecmascript)"));
}),
}]);