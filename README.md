# Nexus Swap DApp

Aplikasi DeFi sederhana untuk swap token di Nexus Layer 1 blockchain. Project ini mendemonstrasikan implementasi smart contract ERC20 dan frontend Next.js untuk berinteraksi dengan blockchain.

## 🚀 Fitur

- **Smart Contracts**: TokenA, TokenB (ERC20), dan <PERSON>wap contract
- **Frontend Modern**: Next.js dengan TypeScript dan Tailwind CSS
- **Wallet Integration**: Koneksi dengan MetaMask dan wallet lainnya
- **Real-time Updates**: Balance dan transaksi terupdate secara real-time
- **1:1 Swap**: Pertukaran token dengan rasio 1:1

## 📁 Struktur Project

```
nexus-swap/
├── contracts/              # Smart contracts Solidity
│   ├── TokenA.sol          # ERC20 Token A
│   ├── TokenB.sol          # ERC20 Token B
│   └── TokenSwap.sol       # Contract untuk swap tokens
├── scripts/                # Deployment scripts
│   └── deploy.js           # Script deploy ke Nexus network
├── frontend/               # Next.js frontend application
│   ├── src/app/page.tsx    # Main swap interface
│   └── lib/                # Utilities dan contract addresses
├── hardhat.config.js       # Konfigurasi Hardhat
├── deployedAddresses.json  # Contract addresses yang sudah di-deploy
└── .env                    # Environment variables (private key)
```

## 🛠️ Setup dan Installation

### 1. Clone dan Install Dependencies

```bash
# Install dependencies untuk smart contracts
npm install

# Install dependencies untuk frontend
cd frontend
npm install
cd ..
```

### 2. Konfigurasi Environment

Buat file `.env` di root directory:

```env
PRIVATE_KEY=your_private_key_here
```

**⚠️ Keamanan**: Jangan pernah commit private key ke repository!

### 3. Compile Smart Contracts

```bash
npx hardhat compile
```

### 4. Deploy ke Nexus Network

```bash
npx hardhat run scripts/deploy.js --network nexus
```

### 5. Jalankan Frontend

```bash
cd frontend
npm run dev
```

Frontend akan berjalan di `http://localhost:3000`

## 📋 Contract Addresses (Deployed)

- **TokenA**: `******************************************`
- **TokenB**: `******************************************`
- **TokenSwap**: `******************************************`
- **Network**: Nexus Testnet III

## 🔧 Cara Menggunakan

### 1. Setup Wallet
- Install MetaMask atau wallet yang kompatibel
- Tambahkan Nexus network:
  - RPC URL: `https://testnet3.rpc.nexus.xyz`
  - Chain ID: `3940`
  - Currency: `NEX`

### 2. Dapatkan Test NEX
- Kunjungi [Nexus Faucet](https://faucet.nexus.xyz) untuk mendapatkan test NEX
- Atau earn NEX melalui [Proving](https://nexus.xyz/proving)

### 3. Menggunakan DApp
1. Buka `http://localhost:3000`
2. Klik "Connect Wallet"
3. Masukkan jumlah Token A yang ingin di-swap
4. Klik "Swap" dan konfirmasi transaksi
5. Balance akan terupdate otomatis

## 🏗️ Arsitektur Smart Contract

### TokenA & TokenB
- Standard ERC20 tokens menggunakan OpenZeppelin
- Initial supply: 1,000,000 tokens masing-masing
- Decimals: 18

### TokenSwap
- Melakukan swap 1:1 antara TokenA dan TokenB
- Menggunakan approval pattern untuk keamanan
- Pre-loaded dengan 500,000 TokenB sebagai liquidity

## 🔍 Monitoring

Pantau transaksi dan contract di [Nexus Explorer](https://explorer.nexus.xyz):
- Cari contract address untuk melihat detail
- Monitor transaksi swap
- Cek balance dan transfer history

## 🧪 Testing

Untuk menjalankan tests:

```bash
npx hardhat test
```

## 📚 Resources

- [Nexus Documentation](https://docs.nexus.xyz)
- [Hardhat Documentation](https://hardhat.org/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [OpenZeppelin Contracts](https://docs.openzeppelin.com/contracts)

## 🤝 Contributing

1. Fork repository
2. Buat feature branch
3. Commit changes
4. Push ke branch
5. Buat Pull Request

## 📄 License

MIT License - lihat file LICENSE untuk detail.

---

**⚡ Powered by Nexus Layer 1 Blockchain**
