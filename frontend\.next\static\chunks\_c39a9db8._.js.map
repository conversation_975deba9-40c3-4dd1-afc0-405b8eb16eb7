{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/contract/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ethers } from 'ethers';\nimport deployedAddresses from '../../lib/deployedAddresses.json';\n\n// Import ABIs\nconst TokenA = {\n  abi: [\n    \"function balanceOf(address owner) view returns (uint256)\",\n    \"function approve(address spender, uint256 amount) returns (bool)\"\n  ]\n};\n\nconst TokenB = {\n  abi: [\n    \"function balanceOf(address owner) view returns (uint256)\"\n  ]\n};\n\nconst TokenSwap = {\n  abi: [\n    \"function swap(uint256 amount) returns (bool)\"\n  ]\n};\n\nexport default function Home() {\n  const [account, setAccount] = useState('');\n  const [tokenABalance, setTokenABalance] = useState('0');\n  const [tokenBBalance, setTokenBBalance] = useState('0');\n  const [swapAmount, setSwapAmount] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    checkIfWalletIsConnected();\n  }, []);\n\n  useEffect(() => {\n    if (account) {\n      updateBalances();\n    }\n  }, [account]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  async function checkIfWalletIsConnected() {\n    try {\n      const { ethereum } = window as any;\n      if (!ethereum) return;\n\n      const accounts = await ethereum.request({ method: 'eth_accounts' });\n      if (accounts.length > 0) {\n        setAccount(accounts[0]);\n      }\n    } catch (error) {\n      console.error(error);\n    }\n  }\n\n  async function connectWallet() {\n    try {\n      const { ethereum } = window as any;\n      if (!ethereum) return;\n\n      const accounts = await ethereum.request({ method: 'eth_requestAccounts' });\n      setAccount(accounts[0]);\n    } catch (error) {\n      console.error(error);\n    }\n  }\n\n  async function updateBalances() {\n    try {\n      const { ethereum } = window as any;\n      const provider = new ethers.BrowserProvider(ethereum);\n      const signer = await provider.getSigner();\n\n      const tokenAContract = new ethers.Contract(deployedAddresses.TokenA, TokenA.abi, signer);\n      const tokenBContract = new ethers.Contract(deployedAddresses.TokenB, TokenB.abi, signer);\n\n      const balanceA = await tokenAContract.balanceOf(account);\n      const balanceB = await tokenBContract.balanceOf(account);\n\n      setTokenABalance(ethers.formatEther(balanceA));\n      setTokenBBalance(ethers.formatEther(balanceB));\n    } catch (error) {\n      console.error(error);\n    }\n  }\n\n  async function handleSwap() {\n    if (!swapAmount) return;\n    setLoading(true);\n\n    try {\n      const { ethereum } = window as any;\n      const provider = new ethers.BrowserProvider(ethereum);\n      const signer = await provider.getSigner();\n\n      const tokenAContract = new ethers.Contract(deployedAddresses.TokenA, TokenA.abi, signer);\n      const swapContract = new ethers.Contract(deployedAddresses.TokenSwap, TokenSwap.abi, signer);\n\n      const amount = ethers.parseEther(swapAmount);\n\n      // First approve TokenSwap contract to spend TokenA\n      const approveTx = await tokenAContract.approve(deployedAddresses.TokenSwap, amount);\n      await approveTx.wait();\n\n      // Then perform the swap\n      const swapTx = await swapContract.swap(amount);\n      await swapTx.wait();\n\n      // Update balances after swap\n      await updateBalances();\n      setSwapAmount('');\n    } catch (error) {\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white relative\">\n      {/* Connection status */}\n      <div className=\"absolute top-6 right-6\">\n        <p className=\"text-sm text-gray-600\">\n          {account ? 'Connected' : 'Not Connected'}\n        </p>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col items-center justify-center min-h-screen\">\n        <h1 className=\"text-5xl font-normal mb-12 text-black\">Nexus Swap</h1>\n\n        {!account ? (\n          <button\n            type=\"button\"\n            onClick={connectWallet}\n            className=\"bg-black text-white px-6 py-3 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors\"\n          >\n            Connect Wallet\n          </button>\n        ) : (\n          <div className=\"space-y-8 w-full max-w-sm\">\n            <div className=\"grid grid-cols-2 gap-6\">\n              <div>\n                <p className=\"text-sm text-gray-600 mb-1\">Token A Balance</p>\n                <p className=\"font-mono text-xl\">{tokenABalance}</p>\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600 mb-1\">Token B Balance</p>\n                <p className=\"font-mono text-xl\">{tokenBBalance}</p>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm text-gray-600 mb-2\">\n                Swap Amount (Token A)\n              </label>\n              <input\n                type=\"number\"\n                value={swapAmount}\n                onChange={(e) => setSwapAmount(e.target.value)}\n                placeholder=\"0.0\"\n                className=\"w-full bg-gray-50 border border-gray-200 px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-black font-mono\"\n              />\n            </div>\n\n            <button\n              type=\"button\"\n              onClick={handleSwap}\n              disabled={loading || !swapAmount}\n              className=\"w-full bg-black text-white py-3 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors disabled:bg-gray-300\"\n            >\n              {loading ? 'Processing...' : 'Swap'}\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,cAAc;AACd,MAAM,SAAS;IACb,KAAK;QACH;QACA;KACD;AACH;AAEA,MAAM,SAAS;IACb,KAAK;QACH;KACD;AACH;AAEA,MAAM,YAAY;IAChB,KAAK;QACH;KACD;AACH;AAEe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,SAAS;gBACX;YACF;QACF;yBAAG;QAAC;KAAQ,GAAG,kDAAkD;IAEjE,eAAe;QACb,IAAI;YACF,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,IAAI,CAAC,UAAU;YAEf,MAAM,WAAW,MAAM,SAAS,OAAO,CAAC;gBAAE,QAAQ;YAAe;YACjE,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,WAAW,QAAQ,CAAC,EAAE;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,eAAe;QACb,IAAI;YACF,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,IAAI,CAAC,UAAU;YAEf,MAAM,WAAW,MAAM,SAAS,OAAO,CAAC;gBAAE,QAAQ;YAAsB;YACxE,WAAW,QAAQ,CAAC,EAAE;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,eAAe;QACb,IAAI;YACF,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,MAAM,WAAW,IAAI,mLAAA,CAAA,SAAM,CAAC,eAAe,CAAC;YAC5C,MAAM,SAAS,MAAM,SAAS,SAAS;YAEvC,MAAM,iBAAiB,IAAI,mLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gGAAA,CAAA,UAAiB,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE;YACjF,MAAM,iBAAiB,IAAI,mLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gGAAA,CAAA,UAAiB,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE;YAEjF,MAAM,WAAW,MAAM,eAAe,SAAS,CAAC;YAChD,MAAM,WAAW,MAAM,eAAe,SAAS,CAAC;YAEhD,iBAAiB,mLAAA,CAAA,SAAM,CAAC,WAAW,CAAC;YACpC,iBAAiB,mLAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,eAAe;QACb,IAAI,CAAC,YAAY;QACjB,WAAW;QAEX,IAAI;YACF,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,MAAM,WAAW,IAAI,mLAAA,CAAA,SAAM,CAAC,eAAe,CAAC;YAC5C,MAAM,SAAS,MAAM,SAAS,SAAS;YAEvC,MAAM,iBAAiB,IAAI,mLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gGAAA,CAAA,UAAiB,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE;YACjF,MAAM,eAAe,IAAI,mLAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,gGAAA,CAAA,UAAiB,CAAC,SAAS,EAAE,UAAU,GAAG,EAAE;YAErF,MAAM,SAAS,mLAAA,CAAA,SAAM,CAAC,UAAU,CAAC;YAEjC,mDAAmD;YACnD,MAAM,YAAY,MAAM,eAAe,OAAO,CAAC,gGAAA,CAAA,UAAiB,CAAC,SAAS,EAAE;YAC5E,MAAM,UAAU,IAAI;YAEpB,wBAAwB;YACxB,MAAM,SAAS,MAAM,aAAa,IAAI,CAAC;YACvC,MAAM,OAAO,IAAI;YAEjB,6BAA6B;YAC7B,MAAM;YACN,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BACV,UAAU,cAAc;;;;;;;;;;;0BAK7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;oBAErD,CAAC,wBACA,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;6CAID,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAEpC,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;0CAItC,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAmC;;;;;;kDAGpD,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU,WAAW,CAAC;gCACtB,WAAU;0CAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GA1JwB;KAAA", "debugId": null}}]}