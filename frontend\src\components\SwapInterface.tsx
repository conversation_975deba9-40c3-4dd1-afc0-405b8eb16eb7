'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';
import deployedAddresses from '../../lib/deployedAddresses.json';

// Type for ethereum object
interface EthereumProvider {
  request: (args: { method: string; params?: unknown[] }) => Promise<unknown>;
  isMetaMask?: boolean;
}

// Import ABIs
const TokenA = {
  abi: [
    "function balanceOf(address owner) view returns (uint256)",
    "function approve(address spender, uint256 amount) returns (bool)"
  ]
};

const TokenB = {
  abi: [
    "function balanceOf(address owner) view returns (uint256)"
  ]
};

const TokenSwap = {
  abi: [
    "function swap(uint256 amount) returns (bool)"
  ]
};

export default function SwapInterface() {
  const [mounted, setMounted] = useState(false);
  const [account, setAccount] = useState('');
  const [tokenABalance, setTokenABalance] = useState('0');
  const [tokenBBalance, setTokenBBalance] = useState('0');
  const [swapAmount, setSwapAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [needsTokens, setNeedsTokens] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Delay wallet check to avoid hydration issues with extensions
    const timer = setTimeout(() => {
      checkIfWalletIsConnected();
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (account && mounted) {
      updateBalances();
    }
  }, [account, mounted]); // eslint-disable-line react-hooks/exhaustive-deps

  async function checkIfWalletIsConnected() {
    try {
      if (typeof window === 'undefined') return;
      const { ethereum } = window as { ethereum?: EthereumProvider };
      if (!ethereum) return;

      const accounts = await ethereum.request({ method: 'eth_accounts' }) as string[];
      if (accounts.length > 0) {
        setAccount(accounts[0]);
      }
    } catch (error) {
      console.error(error);
    }
  }

  async function connectWallet() {
    try {
      if (typeof window === 'undefined') return;
      const { ethereum } = window as { ethereum?: EthereumProvider };
      if (!ethereum) return;

      const accounts = await ethereum.request({ method: 'eth_requestAccounts' }) as string[];
      setAccount(accounts[0]);
    } catch (error) {
      console.error(error);
    }
  }

  async function updateBalances() {
    try {
      if (typeof window === 'undefined') return;
      const { ethereum } = window as { ethereum?: EthereumProvider };
      if (!ethereum) return;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      // Check network
      const network = await provider.getNetwork();
      console.log('Current network:', network.chainId);
      
      if (network.chainId !== BigInt(3940)) {
        console.error('Please switch to Nexus network (Chain ID: 3940)');
        setTokenABalance('Switch to Nexus');
        setTokenBBalance('Switch to Nexus');
        return;
      }

      const tokenAContract = new ethers.Contract(deployedAddresses.TokenA, TokenA.abi, signer);
      const tokenBContract = new ethers.Contract(deployedAddresses.TokenB, TokenB.abi, signer);

      try {
        const balanceA = await tokenAContract.balanceOf(account);
        const formattedBalanceA = ethers.formatEther(balanceA);
        setTokenABalance(formattedBalanceA);
        
        // Check if user needs tokens
        if (parseFloat(formattedBalanceA) === 0) {
          setNeedsTokens(true);
        } else {
          setNeedsTokens(false);
        }
      } catch (error) {
        console.error('Error getting TokenA balance:', error);
        setTokenABalance('0');
        setNeedsTokens(true);
      }

      try {
        const balanceB = await tokenBContract.balanceOf(account);
        setTokenBBalance(ethers.formatEther(balanceB));
      } catch (error) {
        console.error('Error getting TokenB balance:', error);
        setTokenBBalance('0');
      }
    } catch (error) {
      console.error('Error in updateBalances:', error);
      setTokenABalance('Error');
      setTokenBBalance('Error');
    }
  }

  async function handleSwap() {
    if (!swapAmount) return;
    setLoading(true);

    try {
      if (typeof window === 'undefined') return;
      const { ethereum } = window as { ethereum?: EthereumProvider };
      if (!ethereum) return;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const tokenAContract = new ethers.Contract(deployedAddresses.TokenA, TokenA.abi, signer);
      const swapContract = new ethers.Contract(deployedAddresses.TokenSwap, TokenSwap.abi, signer);

      const amount = ethers.parseEther(swapAmount);

      // First approve TokenSwap contract to spend TokenA
      const approveTx = await tokenAContract.approve(deployedAddresses.TokenSwap, amount);
      await approveTx.wait();

      // Then perform the swap
      const swapTx = await swapContract.swap(amount);
      await swapTx.wait();

      // Update balances after swap
      await updateBalances();
      setSwapAmount('');
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }

  // Prevent hydration errors by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen bg-white relative">
        <div className="flex flex-col items-center justify-center min-h-screen">
          <h1 className="text-5xl font-normal mb-12 text-black">Nexus Swap</h1>
          <div className="text-gray-500">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black relative overflow-hidden" suppressHydrationWarning>
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-900/20 via-transparent to-cyan-900/20"></div>
        <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-br from-cyan-400 to-blue-600 transform rotate-45 opacity-10 animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-gradient-to-br from-pink-400 to-red-600 transform -rotate-12 opacity-10 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-600 transform rotate-12 opacity-10 animate-pulse delay-500"></div>
      </div>

      {/* Geometric shapes */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-32 right-32 w-8 h-8 border-4 border-cyan-400 transform rotate-45 animate-bounce"></div>
        <div className="absolute bottom-40 left-40 w-6 h-6 bg-pink-500 transform rotate-12 animate-pulse"></div>
        <div className="absolute top-1/3 left-1/3 w-4 h-4 bg-yellow-400 transform -rotate-45"></div>
      </div>

      {/* Connection status - floating brutal style */}
      <div className="absolute top-8 right-8 z-50">
        <div className={`px-6 py-3 font-black text-sm uppercase tracking-wider transform rotate-3 border-4 shadow-lg transition-all duration-300 ${
          account
            ? 'bg-green-400 border-green-600 text-green-900 shadow-green-400/50 animate-pulse'
            : 'bg-red-400 border-red-600 text-red-900 shadow-red-400/50'
        }`}>
          {account ? '● ONLINE' : '○ OFFLINE'}
        </div>
      </div>

      {/* Main brutal container */}
      <div className="min-h-screen flex items-center justify-center p-4 relative z-10">
        <div className="w-full max-w-6xl">
          {/* Title section with extreme brutalism */}
          <div className="text-center mb-16 relative">
            <div className="relative inline-block">
              <h1 className="text-8xl md:text-9xl font-black text-white mb-4 transform -rotate-2 drop-shadow-2xl relative z-10">
                NEXUS
              </h1>
              <div className="absolute inset-0 text-8xl md:text-9xl font-black text-cyan-400 transform rotate-1 translate-x-2 translate-y-2 -z-10">
                NEXUS
              </div>
            </div>
            <div className="relative inline-block">
              <h2 className="text-5xl md:text-6xl font-black text-cyan-400 transform rotate-1 drop-shadow-xl relative z-10">
                SWAP
              </h2>
              <div className="absolute inset-0 text-5xl md:text-6xl font-black text-pink-500 transform -rotate-1 translate-x-1 translate-y-1 -z-10">
                SWAP
              </div>
            </div>
            <div className="flex justify-center mt-8 space-x-4">
              <div className="w-16 h-2 bg-gradient-to-r from-pink-500 to-cyan-500 transform rotate-1"></div>
              <div className="w-16 h-2 bg-gradient-to-r from-yellow-500 to-red-500 transform -rotate-1"></div>
              <div className="w-16 h-2 bg-gradient-to-r from-green-500 to-blue-500 transform rotate-2"></div>
            </div>
          </div>

          {/* Main content area */}
          {!account ? (
            <div className="text-center">
              <div className="relative inline-block group">
                <button
                  type="button"
                  onClick={connectWallet}
                  className="relative bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-16 py-8 text-2xl font-black uppercase tracking-wider transform -rotate-2 hover:rotate-0 transition-all duration-500 shadow-2xl hover:shadow-cyan-500/50 border-8 border-white z-10"
                >
                  CONNECT WALLET
                </button>
                <div className="absolute inset-0 bg-gradient-to-r from-pink-500 to-red-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500"></div>
              </div>
              <div className="mt-12 flex justify-center space-x-8">
                <div className="text-center">
                  <div className="w-4 h-4 bg-cyan-400 mx-auto mb-2 transform rotate-45"></div>
                  <p className="text-gray-400 font-mono text-sm">SECURE</p>
                </div>
                <div className="text-center">
                  <div className="w-4 h-4 bg-pink-400 mx-auto mb-2 transform rotate-45"></div>
                  <p className="text-gray-400 font-mono text-sm">FAST</p>
                </div>
                <div className="text-center">
                  <div className="w-4 h-4 bg-yellow-400 mx-auto mb-2 transform rotate-45"></div>
                  <p className="text-gray-400 font-mono text-sm">BRUTAL</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 w-full max-w-7xl">
              {/* Balance Cards - Left Column */}
              <div className="space-y-6">
                <div className="relative group">
                  <div className="bg-gradient-to-br from-purple-600 to-pink-600 p-8 transform -rotate-3 border-8 border-white shadow-2xl relative z-10 group-hover:rotate-0 transition-transform duration-500">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-white font-black text-lg uppercase tracking-wider">TOKEN A</h3>
                      <div className="w-6 h-6 bg-white transform rotate-45"></div>
                    </div>
                    <p className="font-mono text-4xl font-black text-white mb-2">{tokenABalance}</p>
                    <div className="w-full h-2 bg-white"></div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-cyan-600 to-blue-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500"></div>
                </div>

                <div className="relative group">
                  <div className="bg-gradient-to-br from-cyan-600 to-blue-600 p-8 transform rotate-2 border-8 border-white shadow-2xl relative z-10 group-hover:rotate-0 transition-transform duration-500">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-white font-black text-lg uppercase tracking-wider">TOKEN B</h3>
                      <div className="w-6 h-6 bg-white transform -rotate-45"></div>
                    </div>
                    <p className="font-mono text-4xl font-black text-white mb-2">{tokenBBalance}</p>
                    <div className="w-full h-2 bg-white"></div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-yellow-600 to-orange-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500"></div>
                </div>
              </div>

              {/* Swap Form - Center Column */}
              <div className="space-y-8">
                <div className="relative group">
                  <div className="bg-black p-8 transform rotate-1 border-8 border-white shadow-2xl relative z-10 group-hover:rotate-0 transition-transform duration-500">
                    <h3 className="text-white font-black text-xl uppercase tracking-wider mb-6 flex items-center">
                      <span className="mr-4">SWAP AMOUNT</span>
                      <div className="w-4 h-4 bg-cyan-400 transform rotate-45"></div>
                    </h3>
                    <input
                      type="number"
                      value={swapAmount}
                      onChange={(e) => setSwapAmount(e.target.value)}
                      placeholder="0.0"
                      className="w-full bg-white text-black px-6 py-6 text-3xl font-black font-mono border-8 border-gray-800 focus:outline-none focus:border-cyan-400 transform -rotate-1 focus:rotate-0 transition-transform duration-300"
                    />
                    <div className="mt-6 flex justify-between items-center">
                      <span className="text-gray-400 font-mono text-sm">TOKEN A → TOKEN B</span>
                      <div className="flex space-x-2">
                        <div className="w-3 h-3 bg-cyan-400 transform rotate-45"></div>
                        <div className="w-3 h-3 bg-pink-400 transform rotate-45"></div>
                        <div className="w-3 h-3 bg-yellow-400 transform rotate-45"></div>
                      </div>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-600 to-pink-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500"></div>
                </div>

                {/* Swap Button */}
                <div className="relative group">
                  <button
                    type="button"
                    onClick={handleSwap}
                    disabled={loading || !swapAmount}
                    className="relative w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-8 text-3xl font-black uppercase tracking-wider transform -rotate-1 hover:rotate-0 transition-all duration-500 shadow-2xl hover:shadow-green-500/50 border-8 border-white disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed z-10"
                  >
                    {loading ? '⚡ PROCESSING...' : '🚀 SWAP NOW'}
                  </button>
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500"></div>
                </div>
              </div>

              {/* Info Panel - Right Column */}
              <div className="space-y-6">
                {needsTokens && (
                  <div className="relative group">
                    <div className="bg-gradient-to-r from-yellow-400 to-orange-500 p-8 transform rotate-2 border-8 border-white shadow-2xl relative z-10 group-hover:rotate-0 transition-transform duration-500">
                      <h3 className="text-black font-black text-xl uppercase tracking-wider mb-4 flex items-center">
                        <span className="mr-4">⚠ NEED TOKENS?</span>
                      </h3>
                      <p className="text-black font-bold mb-6 text-lg">
                        You need TokenA to test the swap functionality. Contact the deployer to get test tokens.
                      </p>
                      <div className="bg-black p-4 transform -rotate-1">
                        <p className="text-green-400 font-mono text-sm break-all">
                          {account}
                        </p>
                      </div>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-pink-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500"></div>
                  </div>
                )}

                {/* Stats Panel */}
                <div className="relative group">
                  <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-8 transform -rotate-1 border-8 border-white shadow-2xl relative z-10 group-hover:rotate-0 transition-transform duration-500">
                    <h3 className="text-white font-black text-xl uppercase tracking-wider mb-6">STATS</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400 font-mono">NETWORK</span>
                        <span className="text-cyan-400 font-black">NEXUS</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400 font-mono">RATIO</span>
                        <span className="text-pink-400 font-black">1:1</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400 font-mono">FEE</span>
                        <span className="text-green-400 font-black">0%</span>
                      </div>
                    </div>
                    <div className="mt-6 w-full h-2 bg-gradient-to-r from-cyan-400 via-pink-400 to-yellow-400"></div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-cyan-600 to-blue-600 transform translate-x-4 translate-y-4 border-8 border-white group-hover:translate-x-2 group-hover:translate-y-2 transition-transform duration-500"></div>
                </div>
              </div>
            </div>
          )}

          {/* Footer - Brutal style */}
          <div className="mt-20 text-center">
            <div className="flex justify-center space-x-8 mb-8">
              <div className="w-8 h-8 bg-cyan-400 transform rotate-45 animate-pulse"></div>
              <div className="w-8 h-8 bg-pink-400 transform -rotate-45 animate-pulse delay-200"></div>
              <div className="w-8 h-8 bg-yellow-400 transform rotate-45 animate-pulse delay-400"></div>
            </div>
            <p className="text-gray-500 font-mono text-sm uppercase tracking-wider">
              POWERED BY NEXUS BLOCKCHAIN
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
