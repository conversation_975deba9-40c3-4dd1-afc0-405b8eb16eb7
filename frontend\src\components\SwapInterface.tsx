'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';
import deployedAddresses from '../../lib/deployedAddresses.json';

// Type for ethereum object
interface EthereumProvider {
  request: (args: { method: string; params?: unknown[] }) => Promise<unknown>;
  isMetaMask?: boolean;
}

// Import ABIs
const TokenA = {
  abi: [
    "function balanceOf(address owner) view returns (uint256)",
    "function approve(address spender, uint256 amount) returns (bool)"
  ]
};

const TokenB = {
  abi: [
    "function balanceOf(address owner) view returns (uint256)"
  ]
};

const TokenSwap = {
  abi: [
    "function swap(uint256 amount) returns (bool)"
  ]
};

export default function SwapInterface() {
  const [account, setAccount] = useState('');
  const [tokenABalance, setTokenABalance] = useState('0');
  const [tokenBBalance, setTokenBBalance] = useState('0');
  const [swapAmount, setSwapAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [needsTokens, setNeedsTokens] = useState(false);

  useEffect(() => {
    checkIfWalletIsConnected();
  }, []);

  useEffect(() => {
    if (account) {
      updateBalances();
    }
  }, [account]); // eslint-disable-line react-hooks/exhaustive-deps

  async function checkIfWalletIsConnected() {
    try {
      if (typeof window === 'undefined') return;
      const { ethereum } = window as { ethereum?: EthereumProvider };
      if (!ethereum) return;

      const accounts = await ethereum.request({ method: 'eth_accounts' }) as string[];
      if (accounts.length > 0) {
        setAccount(accounts[0]);
      }
    } catch (error) {
      console.error(error);
    }
  }

  async function connectWallet() {
    try {
      if (typeof window === 'undefined') return;
      const { ethereum } = window as { ethereum?: EthereumProvider };
      if (!ethereum) return;

      const accounts = await ethereum.request({ method: 'eth_requestAccounts' }) as string[];
      setAccount(accounts[0]);
    } catch (error) {
      console.error(error);
    }
  }

  async function updateBalances() {
    try {
      if (typeof window === 'undefined') return;
      const { ethereum } = window as { ethereum?: EthereumProvider };
      if (!ethereum) return;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      // Check network
      const network = await provider.getNetwork();
      console.log('Current network:', network.chainId);
      
      if (network.chainId !== BigInt(3940)) {
        console.error('Please switch to Nexus network (Chain ID: 3940)');
        setTokenABalance('Switch to Nexus');
        setTokenBBalance('Switch to Nexus');
        return;
      }

      const tokenAContract = new ethers.Contract(deployedAddresses.TokenA, TokenA.abi, signer);
      const tokenBContract = new ethers.Contract(deployedAddresses.TokenB, TokenB.abi, signer);

      try {
        const balanceA = await tokenAContract.balanceOf(account);
        const formattedBalanceA = ethers.formatEther(balanceA);
        setTokenABalance(formattedBalanceA);
        
        // Check if user needs tokens
        if (parseFloat(formattedBalanceA) === 0) {
          setNeedsTokens(true);
        } else {
          setNeedsTokens(false);
        }
      } catch (error) {
        console.error('Error getting TokenA balance:', error);
        setTokenABalance('0');
        setNeedsTokens(true);
      }

      try {
        const balanceB = await tokenBContract.balanceOf(account);
        setTokenBBalance(ethers.formatEther(balanceB));
      } catch (error) {
        console.error('Error getting TokenB balance:', error);
        setTokenBBalance('0');
      }
    } catch (error) {
      console.error('Error in updateBalances:', error);
      setTokenABalance('Error');
      setTokenBBalance('Error');
    }
  }

  async function handleSwap() {
    if (!swapAmount) return;
    setLoading(true);

    try {
      if (typeof window === 'undefined') return;
      const { ethereum } = window as { ethereum?: EthereumProvider };
      if (!ethereum) return;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const tokenAContract = new ethers.Contract(deployedAddresses.TokenA, TokenA.abi, signer);
      const swapContract = new ethers.Contract(deployedAddresses.TokenSwap, TokenSwap.abi, signer);

      const amount = ethers.parseEther(swapAmount);

      // First approve TokenSwap contract to spend TokenA
      const approveTx = await tokenAContract.approve(deployedAddresses.TokenSwap, amount);
      await approveTx.wait();

      // Then perform the swap
      const swapTx = await swapContract.swap(amount);
      await swapTx.wait();

      // Update balances after swap
      await updateBalances();
      setSwapAmount('');
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen bg-white relative">
      {/* Connection status */}
      <div className="absolute top-6 right-6">
        <p className="text-sm text-gray-600">
          {account ? 'Connected' : 'Not Connected'}
        </p>
      </div>

      {/* Main content */}
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-5xl font-normal mb-12 text-black">Nexus Swap</h1>
         
        {!account ? (
          <button
            type="button"
            onClick={connectWallet}
            className="bg-black text-white px-6 py-3 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors"
          >
            Connect Wallet
          </button>
        ) : (
          <div className="space-y-8 w-full max-w-sm">
            <div className="grid grid-cols-2 gap-6">
              <div>
                <p className="text-sm text-gray-600 mb-1">Token A Balance</p>
                <p className="font-mono text-xl">{tokenABalance}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 mb-1">Token B Balance</p>
                <p className="font-mono text-xl">{tokenBBalance}</p>
              </div>
            </div>

            {needsTokens && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-yellow-800 mb-2">Need Test Tokens?</h3>
                <p className="text-sm text-yellow-700 mb-3">
                  You need TokenA to test the swap functionality. Contact the deployer to get test tokens.
                </p>
                <p className="text-xs text-yellow-600">
                  Your address: <code className="bg-yellow-100 px-1 rounded">{account}</code>
                </p>
              </div>
            )}

            <div>
              <label className="block text-sm text-gray-600 mb-2">
                Swap Amount (Token A)
              </label>
              <input
                type="number"
                value={swapAmount}
                onChange={(e) => setSwapAmount(e.target.value)}
                placeholder="0.0"
                className="w-full bg-gray-50 border border-gray-200 px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-black font-mono"
              />
            </div>

            <button
              type="button"
              onClick={handleSwap}
              disabled={loading || !swapAmount}
              className="w-full bg-black text-white py-3 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors disabled:bg-gray-300"
            >
              {loading ? 'Processing...' : 'Swap'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
